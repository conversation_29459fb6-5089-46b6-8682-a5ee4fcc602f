const std = @import("std");

pub fn build(b: *std.Build) void {
    const target = b.standardTargetOptions(.{});
    const optimize = b.standardOptimizeOption(.{});

    // 创建 C 库
    const c_lib = b.addStaticLibrary(.{
        .name = "numpy_c_api",
        .target = target,
        .optimize = optimize,
    });

    // 添加 C 源文件
    c_lib.addCSourceFile(.{
        .file = b.path("src/c/numpy_c_api.c"),
        .flags = &[_][]const u8{
            "-std=c99",
            "-fPIC",
        },
    });

    // 添加头文件路径
    c_lib.addIncludePath(b.path("src"));

    // 添加实际的 Python 和 NumPy 头文件路径
    c_lib.addSystemIncludePath(b.path("/Library/Frameworks/Python.framework/Versions/3.13/include/python3.13"));
    c_lib.addSystemIncludePath(b.path("/Library/Frameworks/Python.framework/Versions/3.13/lib/python3.13/site-packages/numpy/_core/include"));

    // 链接 Python 库
    c_lib.linkSystemLibrary("python3.13");
    c_lib.linkLibC();

    // 创建 Zig 库
    const lib = b.addStaticLibrary(.{
        .name = "numpy_z",
        .root_source_file = b.path("src/numpy.zig"),
        .target = target,
        .optimize = optimize,
    });

    // 链接 C 库
    lib.linkLibrary(c_lib);
    lib.addIncludePath(b.path("src"));

    b.installArtifact(lib);
    b.installArtifact(c_lib);

    // 创建测试
    const lib_unit_tests = b.addTest(.{
        .root_source_file = b.path("src/numpy.zig"),
        .target = target,
        .optimize = optimize,
    });
    lib_unit_tests.linkLibrary(c_lib);
    lib_unit_tests.addIncludePath(b.path("src"));

    const run_lib_unit_tests = b.addRunArtifact(lib_unit_tests);
    const test_step = b.step("test", "Run unit tests");
    test_step.dependOn(&run_lib_unit_tests.step);
}
