#define PY_ARRAY_UNIQUE_SYMBOL NUMPY_C_API_ARRAY_API
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include "numpy_c_api.h"
#include <string.h>

// 全局错误状态
static char error_message[1024] = {0};
static int has_error_flag = 0;

// 数据类型常量
const int NUMPY_BOOL = NPY_BOOL;
const int NUMPY_INT8 = NPY_INT8;
const int NUMPY_INT16 = NPY_INT16;
const int NUMPY_INT32 = NPY_INT32;
const int NUMPY_INT64 = NPY_INT64;
const int NUMPY_UINT8 = NPY_UINT8;
const int NUMPY_UINT16 = NPY_UINT16;
const int NUMPY_UINT32 = NPY_UINT32;
const int NUMPY_UINT64 = NPY_UINT64;
const int NUMPY_FLOAT16 = NPY_FLOAT16;
const int NUMPY_FLOAT32 = NPY_FLOAT32;
const int NUMPY_FLOAT64 = NPY_FLOAT64;
const int NUMPY_COMPLEX64 = NPY_COMPLEX64;
const int NUMPY_COMPLEX128 = NPY_COMPLEX128;

const int NUMPY_AXIS_NONE = -1;

// 错误处理辅助函数
static void set_error(const char* msg) {
    has_error_flag = 1;
    strncpy(error_message, msg, sizeof(error_message) - 1);
    error_message[sizeof(error_message) - 1] = '\0';
}

static void check_python_error(void) {
    if (PyErr_Occurred()) {
        PyObject *ptype, *pvalue, *ptraceback;
        PyErr_Fetch(&ptype, &pvalue, &ptraceback);
        
        if (pvalue) {
            PyObject* str_obj = PyObject_Str(pvalue);
            if (str_obj) {
                const char* error_str = PyUnicode_AsUTF8(str_obj);
                if (error_str) {
                    set_error(error_str);
                }
                Py_DECREF(str_obj);
            }
        }
        
        Py_XDECREF(ptype);
        Py_XDECREF(pvalue);
        Py_XDECREF(ptraceback);
    }
}

// 初始化和清理函数
int numpy_c_init(void) {
    if (!Py_IsInitialized()) {
        Py_Initialize();
        if (!Py_IsInitialized()) {
            set_error("Failed to initialize Python");
            return -1;
        }
    }

    // 导入 NumPy
    if (import_array() < 0) {
        check_python_error();
        return -1;
    }

    return 0;
}

void numpy_c_cleanup(void) {
    if (Py_IsInitialized()) {
        Py_Finalize();
    }
}

// 数组创建函数
PyObject* numpy_c_zeros(int nd, npy_intp* dims, int typenum) {
    PyObject* result = PyArray_ZEROS(nd, dims, typenum, 0);
    if (!result) {
        check_python_error();
    }
    return result;
}

PyObject* numpy_c_ones(int nd, npy_intp* dims, int typenum) {
    PyObject* zeros_arr = PyArray_ZEROS(nd, dims, typenum, 0);
    if (!zeros_arr) {
        check_python_error();
        return NULL;
    }
    
    // 填充为1
    PyArrayObject* arr = (PyArrayObject*)zeros_arr;
    npy_intp size = PyArray_SIZE(arr);
    void* data = PyArray_DATA(arr);
    
    switch (typenum) {
        case NPY_FLOAT64: {
            double* ptr = (double*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = 1.0;
            break;
        }
        case NPY_FLOAT32: {
            float* ptr = (float*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = 1.0f;
            break;
        }
        case NPY_INT32: {
            int32_t* ptr = (int32_t*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = 1;
            break;
        }
        case NPY_INT64: {
            int64_t* ptr = (int64_t*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = 1;
            break;
        }
        default:
            set_error("Unsupported type for ones");
            Py_DECREF(zeros_arr);
            return NULL;
    }
    
    return zeros_arr;
}

PyObject* numpy_c_empty(int nd, npy_intp* dims, int typenum) {
    PyObject* result = PyArray_EMPTY(nd, dims, typenum, 0);
    if (!result) {
        check_python_error();
    }
    return result;
}

PyObject* numpy_c_full(int nd, npy_intp* dims, double fill_value, int typenum) {
    PyObject* empty_arr = PyArray_EMPTY(nd, dims, typenum, 0);
    if (!empty_arr) {
        check_python_error();
        return NULL;
    }
    
    // 填充指定值
    PyArrayObject* arr = (PyArrayObject*)empty_arr;
    npy_intp size = PyArray_SIZE(arr);
    void* data = PyArray_DATA(arr);
    
    switch (typenum) {
        case NPY_FLOAT64: {
            double* ptr = (double*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = fill_value;
            break;
        }
        case NPY_FLOAT32: {
            float* ptr = (float*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = (float)fill_value;
            break;
        }
        case NPY_INT32: {
            int32_t* ptr = (int32_t*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = (int32_t)fill_value;
            break;
        }
        case NPY_INT64: {
            int64_t* ptr = (int64_t*)data;
            for (npy_intp i = 0; i < size; i++) ptr[i] = (int64_t)fill_value;
            break;
        }
        default:
            set_error("Unsupported type for full");
            Py_DECREF(empty_arr);
            return NULL;
    }
    
    return empty_arr;
}

PyObject* numpy_c_arange(double start, double stop, double step, int typenum) {
    PyObject* result = PyArray_Arange(start, stop, step, typenum);
    if (!result) {
        check_python_error();
    }
    return result;
}

PyObject* numpy_c_linspace(double start, double stop, npy_intp num, int endpoint, int typenum) {
    // 使用 NumPy 的 Python API 调用 linspace
    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }
    
    PyObject* linspace_func = PyObject_GetAttrString(numpy_module, "linspace");
    Py_DECREF(numpy_module);
    
    if (!linspace_func) {
        check_python_error();
        return NULL;
    }
    
    PyObject* args = PyTuple_New(5);
    PyTuple_SetItem(args, 0, PyFloat_FromDouble(start));
    PyTuple_SetItem(args, 1, PyFloat_FromDouble(stop));
    PyTuple_SetItem(args, 2, PyLong_FromLong(num));
    PyTuple_SetItem(args, 3, endpoint ? Py_True : Py_False);
    Py_INCREF(endpoint ? Py_True : Py_False);
    
    // 创建 dtype 对象
    PyArray_Descr* dtype = PyArray_DescrFromType(typenum);
    PyTuple_SetItem(args, 4, (PyObject*)dtype);
    
    PyObject* result = PyObject_CallObject(linspace_func, args);
    Py_DECREF(linspace_func);
    Py_DECREF(args);
    
    if (!result) {
        check_python_error();
    }
    
    return result;
}

// 数组信息获取函数
int numpy_c_ndim(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Object is not a NumPy array");
        return -1;
    }
    return PyArray_NDIM((PyArrayObject*)arr);
}

npy_intp* numpy_c_shape(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Object is not a NumPy array");
        return NULL;
    }
    return PyArray_DIMS((PyArrayObject*)arr);
}

npy_intp* numpy_c_strides(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Object is not a NumPy array");
        return NULL;
    }
    return PyArray_STRIDES((PyArrayObject*)arr);
}

npy_intp numpy_c_size(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Object is not a NumPy array");
        return -1;
    }
    return PyArray_SIZE((PyArrayObject*)arr);
}

int numpy_c_typenum(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Object is not a NumPy array");
        return -1;
    }
    return PyArray_TYPE((PyArrayObject*)arr);
}

void* numpy_c_data(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Object is not a NumPy array");
        return NULL;
    }
    return PyArray_DATA((PyArrayObject*)arr);
}

// 内存管理辅助函数
void numpy_c_decref(PyObject* obj) {
    if (obj) {
        Py_DECREF(obj);
    }
}

void numpy_c_incref(PyObject* obj) {
    if (obj) {
        Py_INCREF(obj);
    }
}

// 数学运算函数
PyObject* numpy_c_add(PyObject* a, PyObject* b) {
    if (!PyArray_Check(a) || !PyArray_Check(b)) {
        set_error("Arguments must be NumPy arrays");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* add_func = PyObject_GetAttrString(numpy_module, "add");
    Py_DECREF(numpy_module);

    if (!add_func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(2);
    PyTuple_SetItem(args, 0, a);
    PyTuple_SetItem(args, 1, b);
    Py_INCREF(a);
    Py_INCREF(b);

    PyObject* result = PyObject_CallObject(add_func, args);
    Py_DECREF(add_func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

PyObject* numpy_c_subtract(PyObject* a, PyObject* b) {
    if (!PyArray_Check(a) || !PyArray_Check(b)) {
        set_error("Arguments must be NumPy arrays");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "subtract");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(2);
    PyTuple_SetItem(args, 0, a);
    PyTuple_SetItem(args, 1, b);
    Py_INCREF(a);
    Py_INCREF(b);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

PyObject* numpy_c_multiply(PyObject* a, PyObject* b) {
    if (!PyArray_Check(a) || !PyArray_Check(b)) {
        set_error("Arguments must be NumPy arrays");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "multiply");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(2);
    PyTuple_SetItem(args, 0, a);
    PyTuple_SetItem(args, 1, b);
    Py_INCREF(a);
    Py_INCREF(b);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

PyObject* numpy_c_divide(PyObject* a, PyObject* b) {
    if (!PyArray_Check(a) || !PyArray_Check(b)) {
        set_error("Arguments must be NumPy arrays");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "divide");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(2);
    PyTuple_SetItem(args, 0, a);
    PyTuple_SetItem(args, 1, b);
    Py_INCREF(a);
    Py_INCREF(b);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

PyObject* numpy_c_power(PyObject* a, PyObject* b) {
    if (!PyArray_Check(a) || !PyArray_Check(b)) {
        set_error("Arguments must be NumPy arrays");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "power");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(2);
    PyTuple_SetItem(args, 0, a);
    PyTuple_SetItem(args, 1, b);
    Py_INCREF(a);
    Py_INCREF(b);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

// 三角函数
PyObject* numpy_c_sin(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Argument must be a NumPy array");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "sin");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(1);
    PyTuple_SetItem(args, 0, arr);
    Py_INCREF(arr);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

PyObject* numpy_c_cos(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Argument must be a NumPy array");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "cos");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(1);
    PyTuple_SetItem(args, 0, arr);
    Py_INCREF(arr);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

PyObject* numpy_c_sqrt(PyObject* arr) {
    if (!PyArray_Check(arr)) {
        set_error("Argument must be a NumPy array");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "sqrt");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(1);
    PyTuple_SetItem(args, 0, arr);
    Py_INCREF(arr);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

// 线性代数函数
PyObject* numpy_c_dot(PyObject* a, PyObject* b) {
    if (!PyArray_Check(a) || !PyArray_Check(b)) {
        set_error("Arguments must be NumPy arrays");
        return NULL;
    }

    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (!numpy_module) {
        check_python_error();
        return NULL;
    }

    PyObject* func = PyObject_GetAttrString(numpy_module, "dot");
    Py_DECREF(numpy_module);

    if (!func) {
        check_python_error();
        return NULL;
    }

    PyObject* args = PyTuple_New(2);
    PyTuple_SetItem(args, 0, a);
    PyTuple_SetItem(args, 1, b);
    Py_INCREF(a);
    Py_INCREF(b);

    PyObject* result = PyObject_CallObject(func, args);
    Py_DECREF(func);
    Py_DECREF(args);

    if (!result) {
        check_python_error();
    }

    return result;
}

// 错误处理
int numpy_c_has_error(void) {
    return has_error_flag;
}

const char* numpy_c_get_error(void) {
    return error_message;
}

void numpy_c_clear_error(void) {
    has_error_flag = 0;
    error_message[0] = '\0';
}
