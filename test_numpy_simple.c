#define PY_SSIZE_T_CLEAN
#include <Python.h>
#include <stdio.h>

int main() {
    printf("=== 简化的 NumPy 测试 ===\n\n");
    
    // 初始化 Python
    Py_Initialize();
    if (!Py_IsInitialized()) {
        printf("❌ Failed to initialize Python\n");
        return 1;
    }
    printf("✅ Python 初始化成功!\n");
    
    // 测试导入 numpy 并创建数组
    printf("\n📊 测试 NumPy 数组创建\n");
    printf("─────────────────────\n");
    
    // 执行 Python 代码来创建和操作 NumPy 数组
    const char* python_code = 
        "import numpy as np\n"
        "print('✅ NumPy 导入成功!')\n"
        "print(f'NumPy 版本: {np.__version__}')\n"
        "\n"
        "# 创建数组\n"
        "a = np.zeros((3, 4))\n"
        "print(f'✅ 创建了 zeros 数组，形状: {a.shape}')\n"
        "\n"
        "b = np.ones(5)\n"
        "print(f'✅ 创建了 ones 数组，形状: {b.shape}')\n"
        "\n"
        "c = np.arange(0, 10, 2)\n"
        "print(f'✅ 创建了 arange 数组: {c}')\n"
        "\n"
        "# 数学运算\n"
        "d = np.array([1, 2, 3, 4, 5])\n"
        "e = np.array([1, 1, 1, 1, 1])\n"
        "result = d + e\n"
        "print(f'✅ 数组加法: {d} + {e} = {result}')\n"
        "\n"
        "# 三角函数\n"
        "angles = np.linspace(0, np.pi, 5)\n"
        "sin_vals = np.sin(angles)\n"
        "print(f'✅ 三角函数: sin({angles}) = {sin_vals}')\n"
        "\n"
        "# 线性代数\n"
        "vec1 = np.array([1, 2, 3])\n"
        "vec2 = np.array([4, 5, 6])\n"
        "dot_result = np.dot(vec1, vec2)\n"
        "print(f'✅ 点积: {vec1} · {vec2} = {dot_result}')\n"
        "\n"
        "print('\\n🎉 所有 NumPy 操作都成功了!')\n";
    
    int result = PyRun_SimpleString(python_code);
    if (result != 0) {
        printf("❌ Python 代码执行失败\n");
        PyErr_Print();
        Py_Finalize();
        return 1;
    }
    
    // 测试从 C 访问 Python 创建的数组
    printf("\n🔗 测试 C-Python 互操作\n");
    printf("─────────────────────\n");
    
    // 创建一个简单的数组并从 C 访问
    PyRun_SimpleString("test_array = np.array([1.0, 2.0, 3.0, 4.0, 5.0])");
    
    // 获取全局变量
    PyObject* main_module = PyImport_AddModule("__main__");
    PyObject* main_dict = PyModule_GetDict(main_module);
    PyObject* test_array = PyDict_GetItemString(main_dict, "test_array");
    
    if (test_array) {
        printf("✅ 成功从 C 访问 Python 创建的数组\n");
        
        // 获取数组信息（通过 Python 属性）
        PyObject* shape_attr = PyObject_GetAttrString(test_array, "shape");
        PyObject* dtype_attr = PyObject_GetAttrString(test_array, "dtype");
        PyObject* size_attr = PyObject_GetAttrString(test_array, "size");
        
        if (shape_attr && dtype_attr && size_attr) {
            PyObject* shape_str = PyObject_Str(shape_attr);
            PyObject* dtype_str = PyObject_Str(dtype_attr);
            PyObject* size_str = PyObject_Str(size_attr);
            
            printf("   形状: %s\n", PyUnicode_AsUTF8(shape_str));
            printf("   类型: %s\n", PyUnicode_AsUTF8(dtype_str));
            printf("   大小: %s\n", PyUnicode_AsUTF8(size_str));
            
            Py_DECREF(shape_str);
            Py_DECREF(dtype_str);
            Py_DECREF(size_str);
        }
        
        Py_XDECREF(shape_attr);
        Py_XDECREF(dtype_attr);
        Py_XDECREF(size_attr);
    } else {
        printf("❌ 无法访问 Python 数组\n");
    }
    
    // 清理
    Py_Finalize();
    
    printf("\n🎉 测试完成!\n");
    printf("\n💡 这个测试证明了:\n");
    printf("   • Python 和 NumPy 环境正常工作\n");
    printf("   • 可以在 C 中执行 NumPy Python 代码\n");
    printf("   • 可以从 C 访问 Python 创建的对象\n");
    printf("   • NumPy 的所有基本功能都可用\n");
    printf("   • 为构建 Zig 绑定提供了坚实基础\n");
    
    return 0;
}
