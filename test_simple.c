#define PY_SSIZE_T_CLEAN
#include <Python.h>
#include <stdio.h>

int main() {
    printf("Testing Python C API...\n");
    
    // 初始化 Python
    Py_Initialize();
    if (!Py_IsInitialized()) {
        printf("Failed to initialize Python\n");
        return 1;
    }
    
    printf("Python initialized successfully!\n");
    printf("Python version: %s\n", Py_GetVersion());
    
    // 测试导入 numpy
    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (numpy_module == NULL) {
        printf("Failed to import numpy\n");
        PyErr_Print();
        Py_Finalize();
        return 1;
    }
    
    printf("NumPy imported successfully!\n");
    
    // 获取 numpy 版本
    PyObject* version_attr = PyObject_GetAttrString(numpy_module, "__version__");
    if (version_attr) {
        const char* version_str = PyUnicode_AsUTF8(version_attr);
        if (version_str) {
            printf("NumPy version: %s\n", version_str);
        }
        Py_DECREF(version_attr);
    }
    
    // 清理
    Py_DECREF(numpy_module);
    Py_Finalize();
    
    printf("Test completed successfully!\n");
    return 0;
}
