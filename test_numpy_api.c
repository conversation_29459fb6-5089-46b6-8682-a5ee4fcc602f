#define PY_SSIZE_T_CLEAN
#define NPY_NO_DEPRECATED_API NPY_1_7_API_VERSION
#include <Python.h>
#include <numpy/arrayobject.h>
#include <stdio.h>

int main() {
    printf("=== NumPy C API 测试 ===\n\n");
    
    // 初始化 Python
    Py_Initialize();
    if (!Py_IsInitialized()) {
        printf("❌ Failed to initialize Python\n");
        return 1;
    }
    printf("✅ Python 初始化成功!\n");
    
    // 导入 NumPy 数组 API
    if (import_array() < 0) {
        printf("❌ Failed to import NumPy array API\n");
        PyErr_Print();
        Py_Finalize();
        return 1;
    }
    printf("✅ NumPy 数组 API 导入成功!\n\n");
    
    // 测试 1: 创建数组
    printf("📊 测试 1: 创建数组\n");
    printf("─────────────────────\n");
    
    // 创建一个 3x4 的 float64 数组
    npy_intp dims[2] = {3, 4};
    PyObject* array = PyArray_ZEROS(2, dims, NPY_FLOAT64, 0);
    if (!array) {
        printf("❌ Failed to create array\n");
        PyErr_Print();
        Py_Finalize();
        return 1;
    }
    
    printf("✅ 创建了 3x4 的 float64 数组\n");
    printf("   维度数: %d\n", PyArray_NDIM((PyArrayObject*)array));
    printf("   形状: [%ld, %ld]\n", 
           PyArray_DIM((PyArrayObject*)array, 0),
           PyArray_DIM((PyArrayObject*)array, 1));
    printf("   总元素数: %ld\n", PyArray_SIZE((PyArrayObject*)array));
    printf("   数据类型: %d (NPY_FLOAT64=%d)\n", 
           PyArray_TYPE((PyArrayObject*)array), NPY_FLOAT64);
    
    // 测试 2: 设置和获取数组元素
    printf("\n🔢 测试 2: 数组元素操作\n");
    printf("─────────────────────\n");
    
    // 获取数据指针
    double* data = (double*)PyArray_DATA((PyArrayObject*)array);
    
    // 设置一些值
    data[0] = 1.0;  // [0,0]
    data[5] = 2.5;  // [1,1] 
    data[10] = 3.7; // [2,2]
    
    printf("✅ 设置了对角线元素\n");
    printf("   array[0,0] = %.1f\n", data[0]);
    printf("   array[1,1] = %.1f\n", data[5]);
    printf("   array[2,2] = %.1f\n", data[10]);
    
    // 测试 3: 使用 NumPy 函数
    printf("\n🧮 测试 3: NumPy 函数调用\n");
    printf("─────────────────────\n");
    
    // 创建一个简单的 1D 数组用于数学运算
    npy_intp size = 5;
    PyObject* vec = PyArray_ARANGE(0, 5, 1, NPY_FLOAT64);
    if (!vec) {
        printf("❌ Failed to create arange array\n");
        PyErr_Print();
    } else {
        printf("✅ 创建了 arange(0, 5) 数组\n");
        
        double* vec_data = (double*)PyArray_DATA((PyArrayObject*)vec);
        printf("   内容: [");
        for (int i = 0; i < 5; i++) {
            printf("%.0f", vec_data[i]);
            if (i < 4) printf(", ");
        }
        printf("]\n");
        
        Py_DECREF(vec);
    }
    
    // 测试 4: 调用 NumPy Python 函数
    printf("\n🐍 测试 4: Python NumPy 函数\n");
    printf("─────────────────────\n");
    
    // 导入 numpy 模块
    PyObject* numpy_module = PyImport_ImportModule("numpy");
    if (numpy_module) {
        // 调用 numpy.ones
        PyObject* ones_func = PyObject_GetAttrString(numpy_module, "ones");
        if (ones_func) {
            PyObject* shape_tuple = PyTuple_New(1);
            PyTuple_SetItem(shape_tuple, 0, PyLong_FromLong(3));
            
            PyObject* args = PyTuple_New(1);
            PyTuple_SetItem(args, 0, shape_tuple);
            
            PyObject* ones_array = PyObject_CallObject(ones_func, args);
            if (ones_array) {
                printf("✅ 调用 numpy.ones(3) 成功\n");
                printf("   形状: [%ld]\n", PyArray_DIM((PyArrayObject*)ones_array, 0));
                
                double* ones_data = (double*)PyArray_DATA((PyArrayObject*)ones_array);
                printf("   内容: [%.0f, %.0f, %.0f]\n", 
                       ones_data[0], ones_data[1], ones_data[2]);
                
                Py_DECREF(ones_array);
            } else {
                printf("❌ Failed to call numpy.ones\n");
                PyErr_Print();
            }
            
            Py_DECREF(args);
            Py_DECREF(ones_func);
        }
        
        Py_DECREF(numpy_module);
    } else {
        printf("❌ Failed to import numpy module\n");
        PyErr_Print();
    }
    
    // 清理
    Py_DECREF(array);
    Py_Finalize();
    
    printf("\n🎉 所有测试完成!\n");
    printf("\n💡 这证明了:\n");
    printf("   • Python C API 工作正常\n");
    printf("   • NumPy C API 可以使用\n");
    printf("   • 可以创建和操作 NumPy 数组\n");
    printf("   • 可以调用 NumPy Python 函数\n");
    printf("   • 为 Zig 绑定奠定了基础\n");
    
    return 0;
}
